{"name": "agri-lift-backend", "version": "1.0.0", "description": "Backend for Agri-Lift Soil Insight application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "sharp": "^0.33.5", "tesseract.js": "^4.0.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["agriculture", "soil-analysis", "ocr", "crop-recommendation"], "author": "Agri-Lift Team", "license": "MIT"}